<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['share']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['share']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<!-- Share Comment Modal -->
<div id="shareCommentModal-<?php echo e($share->id); ?>" class="fixed inset-0 bg-black bg-opacity-75 overflow-y-auto h-full w-full hidden z-50" onclick="closeShareCommentModal(<?php echo e($share->id); ?>, event)">
    <div class="relative top-4 mx-auto max-w-2xl bg-gray-800 rounded-lg shadow-2xl" onclick="event.stopPropagation()">
        <!-- Modal Header -->
        <div class="flex items-center justify-between p-4 border-b border-gray-700">
            <h3 class="text-lg font-medium text-white">
                <?php echo e($share->user->name); ?>'s shared post
            </h3>
            <button onclick="closeShareCommentModal(<?php echo e($share->id); ?>)" class="text-gray-400 hover:text-white transition-colors">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>

        <!-- Share Content -->
        <div class="p-4 border-b border-gray-700">
            <!-- Share Header -->
            <div class="flex items-center space-x-3 mb-4">
                <a href="<?php echo e(route('profile.user', $share->user)); ?>">
                    <img class="h-10 w-10 rounded-full"
                         src="<?php echo e($share->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($share->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($share->user->name) . '&color=7BC74D&background=EEEEEE'); ?>"
                         alt="<?php echo e($share->user->name); ?>">
                </a>
                <div>
                    <a href="<?php echo e(route('profile.user', $share->user)); ?>" class="font-semibold text-white hover:text-custom-green">
                        <?php echo e($share->user->name); ?>

                    </a>
                    <p class="text-sm text-gray-400"><?php echo e($share->created_at->diffForHumans()); ?></p>
                </div>
            </div>

            <!-- Share Caption -->
            <?php if($share->caption): ?>
                <div class="text-gray-300 mb-4">
                    <?php echo nl2br(e($share->caption)); ?>

                </div>
            <?php endif; ?>

            <!-- Original Post Preview -->
            <div class="bg-gray-700 rounded-lg p-4 border border-gray-600">
                <!-- Original Post Header -->
                <div class="flex items-center space-x-3 mb-3">
                    <?php if($share->post->organization): ?>
                        <img class="h-8 w-8 rounded-full"
                             src="<?php echo e($share->post->organization->logo ? \Illuminate\Support\Facades\Storage::disk('public')->url($share->post->organization->logo) : 'https://ui-avatars.com/api/?name=' . urlencode($share->post->organization->name) . '&color=7BC74D&background=EEEEEE'); ?>"
                             alt="<?php echo e($share->post->organization->name); ?>">
                        <div>
                            <p class="font-semibold text-white text-sm"><?php echo e($share->post->organization->name); ?></p>
                            <p class="text-xs text-gray-400"><?php echo e($share->post->created_at->diffForHumans()); ?></p>
                        </div>
                    <?php else: ?>
                        <img class="h-8 w-8 rounded-full"
                             src="<?php echo e($share->post->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($share->post->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($share->post->user->name) . '&color=7BC74D&background=EEEEEE'); ?>"
                             alt="<?php echo e($share->post->user->name); ?>">
                        <div>
                            <p class="font-semibold text-white text-sm"><?php echo e($share->post->user->name); ?></p>
                            <p class="text-xs text-gray-400"><?php echo e($share->post->created_at->diffForHumans()); ?></p>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Original Post Content -->
                <h4 class="text-lg font-semibold text-white mb-2"><?php echo e($share->post->title); ?></h4>
                <div class="text-gray-300 text-sm">
                    <?php echo e(Str::limit($share->post->content, 200)); ?>

                </div>

                <!-- Original Post Images (if any) -->
                <?php if($share->post->images && count($share->post->images) > 0): ?>
                    <div class="mt-3">
                        <img src="<?php echo e(\Illuminate\Support\Facades\Storage::disk('public')->url($share->post->images[0])); ?>" 
                             alt="Post image" 
                             class="w-full rounded-lg max-h-48 object-cover">
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Engagement Stats -->
        <div class="px-4 py-3 border-b border-gray-700">
            <div class="flex items-center justify-between text-gray-400 text-sm">
                <div class="flex items-center space-x-4">
                    <?php if($share->likes_count > 0): ?>
                        <div class="flex items-center space-x-1">
                            <div class="flex items-center">
                                <div class="w-5 h-5 bg-red-500 rounded-full flex items-center justify-center">
                                    <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                                    </svg>
                                </div>
                            </div>
                            <span id="modal-share-likes-count-<?php echo e($share->id); ?>"><?php echo e($share->likes_count); ?></span>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="flex items-center space-x-4">
                    <span id="modal-share-comments-count-<?php echo e($share->id); ?>"><?php echo e($share->comments_count); ?> comments</span>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="px-4 py-3 border-b border-gray-700">
            <div class="flex items-center justify-around">
                <button onclick="toggleShareLikeInModal(<?php echo e($share->id); ?>)" 
                        class="flex items-center space-x-2 text-gray-400 hover:text-red-500 transition-colors py-2 px-4 rounded-lg hover:bg-gray-700"
                        id="modal-share-like-btn-<?php echo e($share->id); ?>">
                    <svg class="w-5 h-5 <?php echo e($share->isLikedBy(auth()->user()) ? 'text-red-500 fill-current' : ''); ?>" 
                         fill="<?php echo e($share->isLikedBy(auth()->user()) ? 'currentColor' : 'none'); ?>" 
                         stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                    </svg>
                    <span class="font-medium">Like</span>
                </button>
                
                <button class="flex items-center space-x-2 text-gray-400 hover:text-blue-500 transition-colors py-2 px-4 rounded-lg hover:bg-gray-700">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                    <span class="font-medium">Comment</span>
                </button>
                
                <button onclick="viewOriginalPost(<?php echo e($share->post->id); ?>)" 
                        class="flex items-center space-x-2 text-gray-400 hover:text-purple-500 transition-colors py-2 px-4 rounded-lg hover:bg-gray-700">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                    <span class="font-medium">View Original</span>
                </button>
            </div>
        </div>

        <!-- Comments Section -->
        <div class="max-h-96 overflow-y-auto">
            <!-- Sort Options -->
            <div class="px-4 py-3 border-b border-gray-700">
                <select class="bg-gray-700 text-white border border-gray-600 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-custom-green">
                    <option value="most_relevant">Most relevant</option>
                    <option value="newest">Newest</option>
                    <option value="oldest">Oldest</option>
                </select>
            </div>

            <!-- Comments List -->
            <div class="modal-share-comments-list" id="modal-share-comments-list-<?php echo e($share->id); ?>">
                <?php $__empty_1 = true; $__currentLoopData = $share->comments->whereNull('parent_id'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $comment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <?php if (isset($component)) { $__componentOriginal144b887a0a40fb7550e3c201e0a4811b = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal144b887a0a40fb7550e3c201e0a4811b = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modal-share-comment-item','data' => ['comment' => $comment,'share' => $share]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modal-share-comment-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['comment' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($comment),'share' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($share)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal144b887a0a40fb7550e3c201e0a4811b)): ?>
<?php $attributes = $__attributesOriginal144b887a0a40fb7550e3c201e0a4811b; ?>
<?php unset($__attributesOriginal144b887a0a40fb7550e3c201e0a4811b); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal144b887a0a40fb7550e3c201e0a4811b)): ?>
<?php $component = $__componentOriginal144b887a0a40fb7550e3c201e0a4811b; ?>
<?php unset($__componentOriginal144b887a0a40fb7550e3c201e0a4811b); ?>
<?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <div class="text-gray-400 text-center py-8 px-4">
                        <div class="w-16 h-16 mx-auto mb-4 bg-gray-700 rounded-full flex items-center justify-center">
                            <svg class="w-8 h-8 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                            </svg>
                        </div>
                        <p class="text-lg font-medium mb-2">No comments yet</p>
                        <p class="text-sm">Be the first to comment on this shared post.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Comment Input -->
        <div class="p-4 border-t border-gray-700">
            <?php if(auth()->guard()->check()): ?>
                <form class="modal-share-comment-form" data-share-id="<?php echo e($share->id); ?>">
                    <?php echo csrf_field(); ?>
                    <div class="flex space-x-3">
                        <img class="h-8 w-8 rounded-full"
                             src="<?php echo e(auth()->user()->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url(auth()->user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7BC74D&background=EEEEEE'); ?>"
                             alt="<?php echo e(auth()->user()->name); ?>">
                        <div class="flex-1">
                            <textarea name="content" rows="1" 
                                      placeholder="Comment as <?php echo e(auth()->user()->name); ?>"
                                      class="w-full bg-gray-700 text-white border border-gray-600 rounded-full px-4 py-2 focus:outline-none focus:ring-2 focus:ring-custom-green focus:border-transparent resize-none"
                                      style="min-height: 40px;"></textarea>
                        </div>
                    </div>
                </form>
            <?php else: ?>
                <div class="text-center py-4">
                    <p class="text-gray-400 mb-2">Please log in to comment</p>
                    <a href="<?php echo e(route('login')); ?>" class="text-custom-green hover:text-green-400">Log in</a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/components/share-comment-modal.blade.php ENDPATH**/ ?>