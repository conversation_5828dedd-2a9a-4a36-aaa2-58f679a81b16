@props(['comment', 'post'])

<div class="modal-comment-item py-4 px-4 hover:bg-gray-700/30 transition-colors duration-150" data-comment-id="{{ $comment->id }}">
    <div class="flex space-x-3">
        <a href="{{ route('profile.user', $comment->user) }}" class="flex-shrink-0">
            <img class="h-8 w-8 rounded-full"
                 src="{{ $comment->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($comment->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($comment->user->name) . '&color=7BC74D&background=EEEEEE' }}"
                 alt="{{ $comment->user->name }}">
        </a>
        <div class="flex-1 min-w-0">
            <div class="bg-gray-700 rounded-2xl px-4 py-3">
                <div class="flex items-center space-x-2 mb-1">
                    <a href="{{ route('profile.user', $comment->user) }}" class="font-semibold text-white hover:text-custom-green text-sm hover:underline">
                        {{ $comment->user->name }}
                    </a>
                    @if(auth()->check() && (auth()->id() === $comment->user_id || auth()->user()->isAdmin()))
                        <div class="relative ml-auto" x-data="{ open: false }">
                            <button @click="open = !open" class="text-gray-400 hover:text-gray-300 p-1 rounded-full hover:bg-gray-600">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                                </svg>
                            </button>
                            <div x-show="open" @click.away="open = false" x-transition class="absolute right-0 mt-2 w-32 bg-gray-800 rounded-md shadow-lg py-1 z-10 border border-gray-600">
                                <button onclick="editComment({{ $comment->id }})" class="block w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-700">Edit</button>
                                <button onclick="deleteComment({{ $comment->id }})" class="block w-full text-left px-4 py-2 text-sm text-red-400 hover:bg-gray-700">Delete</button>
                            </div>
                        </div>
                    @endif
                </div>

                <div class="comment-content">
                    <p class="text-gray-200 text-sm leading-relaxed">{!! nl2br(e($comment->content)) !!}</p>
                </div>

                <!-- Edit form (hidden by default) -->
                <div class="comment-edit-form hidden mt-3">
                    <form class="edit-comment-form" data-comment-id="{{ $comment->id }}">
                        @csrf
                        @method('PUT')
                        <textarea name="content" rows="2"
                                  class="w-full bg-gray-600 text-white border border-gray-500 rounded-lg focus:ring-custom-green focus:border-custom-green resize-none text-sm p-3">{{ $comment->content }}</textarea>
                        <div class="mt-2 flex justify-end space-x-2">
                            <button type="button" onclick="cancelEditComment({{ $comment->id }})"
                                    class="px-3 py-1.5 text-sm text-gray-400 hover:text-gray-200 rounded-md hover:bg-gray-600 transition-colors">Cancel</button>
                            <button type="submit"
                                    class="px-3 py-1.5 text-sm bg-custom-green text-white rounded-md hover:bg-green-600 transition-colors">Save</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Comment Actions -->
            <div class="flex items-center space-x-4 mt-2 text-xs text-gray-400">
                <span>{{ $comment->created_at->diffForHumans() }}</span>
                <button onclick="toggleCommentLike({{ $comment->id }})"
                        class="hover:text-red-400 transition-colors font-medium {{ $comment->isLikedBy(auth()->user()) ? 'text-red-400' : '' }}"
                        id="comment-like-btn-{{ $comment->id }}">
                    Like
                </button>
                <button onclick="showReplyForm({{ $comment->id }})" class="hover:text-blue-400 transition-colors font-medium">
                    Reply
                </button>
                @if($comment->likes_count > 0)
                    <span class="text-gray-500" id="comment-likes-count-{{ $comment->id }}">
                        {{ $comment->likes_count }} {{ $comment->likes_count === 1 ? 'like' : 'likes' }}
                    </span>
                @endif
            </div>

            <!-- Reply Form (hidden by default) -->
            <div class="reply-form hidden mt-3" id="reply-form-{{ $comment->id }}">
                <form class="comment-form" data-post-id="{{ $post->id }}" data-parent-id="{{ $comment->id }}">
                    @csrf
                    <div class="flex space-x-3">
                        <img class="h-6 w-6 rounded-full"
                             src="{{ auth()->user()->avatar ?? 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name ?? 'User') . '&color=7BC74D&background=EEEEEE' }}"
                             alt="{{ auth()->user()->name ?? 'User' }}">
                        <div class="flex-1">
                            <textarea name="content" rows="1"
                                      placeholder="Reply to {{ $comment->user->name }}"
                                      class="w-full bg-gray-600 text-white border border-gray-500 rounded-full px-3 py-2 focus:outline-none focus:ring-2 focus:ring-custom-green focus:border-transparent resize-none text-sm"
                                      style="min-height: 32px;"></textarea>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Replies Section -->
            @if($comment->replies && $comment->replies->count() > 0)
                <div class="mt-3">
                    <!-- View Replies Button -->
                    <button onclick="toggleModalReplies({{ $comment->id }})" 
                            class="flex items-center space-x-2 text-gray-400 hover:text-gray-200 text-sm font-medium transition-colors"
                            id="modal-view-replies-btn-{{ $comment->id }}">
                        <svg class="w-4 h-4 transform transition-transform" id="modal-replies-arrow-{{ $comment->id }}">
                            <path fill="currentColor" d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
                        </svg>
                        <span id="modal-replies-text-{{ $comment->id }}">
                            View {{ $comment->replies->count() }} {{ $comment->replies->count() === 1 ? 'reply' : 'replies' }}
                        </span>
                    </button>

                    <!-- Replies List (hidden by default) -->
                    <div class="modal-replies-list hidden mt-3 ml-4 border-l-2 border-gray-600 pl-4" id="modal-replies-list-{{ $comment->id }}">
                        @foreach($comment->replies as $reply)
                            <div class="modal-reply-item py-2" data-reply-id="{{ $reply->id }}">
                                <div class="flex space-x-2">
                                    <a href="{{ route('profile.user', $reply->user) }}" class="flex-shrink-0">
                                        <img class="h-6 w-6 rounded-full"
                                             src="{{ $reply->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($reply->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($reply->user->name) . '&color=7BC74D&background=EEEEEE' }}"
                                             alt="{{ $reply->user->name }}">
                                    </a>
                                    <div class="flex-1 min-w-0">
                                        <div class="bg-gray-700 rounded-xl px-3 py-2">
                                            <div class="flex items-center space-x-2 mb-1">
                                                <a href="{{ route('profile.user', $reply->user) }}" class="font-semibold text-white hover:text-custom-green text-xs hover:underline">
                                                    {{ $reply->user->name }}
                                                </a>
                                                @if(auth()->check() && (auth()->id() === $reply->user_id || auth()->user()->isAdmin()))
                                                    <div class="relative ml-auto" x-data="{ open: false }">
                                                        <button @click="open = !open" class="text-gray-400 hover:text-gray-300 p-1 rounded-full hover:bg-gray-600">
                                                            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                                                <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                                                            </svg>
                                                        </button>
                                                        <div x-show="open" @click.away="open = false" x-transition class="absolute right-0 mt-2 w-32 bg-gray-800 rounded-md shadow-lg py-1 z-10 border border-gray-600">
                                                            <button onclick="editModalReply({{ $reply->id }})" class="block w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-700">Edit</button>
                                                            <button onclick="deleteModalReply({{ $reply->id }})" class="block w-full text-left px-4 py-2 text-sm text-red-400 hover:bg-gray-700">Delete</button>
                                                        </div>
                                                    </div>
                                                @endif
                                            </div>
                                            <p class="text-gray-200 text-xs leading-relaxed">{!! nl2br(e($reply->content)) !!}</p>
                                        </div>

                                        <!-- Reply Actions -->
                                        <div class="flex items-center space-x-3 mt-1 text-xs text-gray-400">
                                            <span>{{ $reply->created_at->diffForHumans() }}</span>
                                            <button onclick="toggleModalReplyLike({{ $reply->id }})" 
                                                    class="hover:text-red-400 transition-colors font-medium {{ $reply->isLikedBy(auth()->user()) ? 'text-red-400' : '' }}"
                                                    id="modal-reply-like-btn-{{ $reply->id }}">
                                                Like
                                            </button>
                                            <button onclick="showModalReplyForm({{ $comment->id }})" class="hover:text-blue-400 transition-colors font-medium">
                                                Reply
                                            </button>
                                            @if($reply->likes_count > 0)
                                                <span class="text-gray-500" id="modal-reply-likes-count-{{ $reply->id }}">
                                                    {{ $reply->likes_count }} {{ $reply->likes_count === 1 ? 'like' : 'likes' }}
                                                </span>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>
