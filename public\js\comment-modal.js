// Comment Modal Functions

// Open comment modal
function openCommentModal(postId) {
    document.getElementById(`commentModal-${postId}`).classList.remove('hidden');
    document.body.style.overflow = 'hidden'; // Prevent background scrolling
}

// Close comment modal
function closeCommentModal(postId, event = null) {
    if (event && event.target !== event.currentTarget) {
        return; // Don't close if clicking inside modal content
    }
    document.getElementById(`commentModal-${postId}`).classList.add('hidden');
    document.body.style.overflow = 'auto'; // Restore scrolling
}

// Open share comment modal
function openShareCommentModal(shareId) {
    document.getElementById(`shareCommentModal-${shareId}`).classList.remove('hidden');
    document.body.style.overflow = 'hidden';
}

// Close share comment modal
function closeShareCommentModal(shareId, event = null) {
    if (event && event.target !== event.currentTarget) {
        return;
    }
    document.getElementById(`shareCommentModal-${shareId}`).classList.add('hidden');
    document.body.style.overflow = 'auto';
}

// Toggle like in modal
async function toggleLikeInModal(postId) {
    try {
        const response = await fetch(`/posts/${postId}/like`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        const data = await response.json();
        
        if (data.success) {
            // Update modal like button
            const modalLikeBtn = document.getElementById(`modal-like-btn-${postId}`);
            const modalLikesCount = document.getElementById(`modal-likes-count-${postId}`);
            const svg = modalLikeBtn.querySelector('svg');
            
            if (data.liked) {
                svg.classList.add('text-red-500', 'fill-current');
                svg.setAttribute('fill', 'currentColor');
            } else {
                svg.classList.remove('text-red-500', 'fill-current');
                svg.setAttribute('fill', 'none');
            }
            
            if (modalLikesCount) {
                modalLikesCount.textContent = data.likes_count;
            }
            
            // Also update the main post card
            const mainLikeBtn = document.getElementById(`like-btn-${postId}`);
            const mainLikesCount = document.getElementById(`like-count-${postId}`);
            if (mainLikeBtn && mainLikesCount) {
                const mainSvg = mainLikeBtn.querySelector('svg');
                if (data.liked) {
                    mainSvg.classList.add('text-red-600', 'fill-current');
                    mainSvg.setAttribute('fill', 'currentColor');
                } else {
                    mainSvg.classList.remove('text-red-600', 'fill-current');
                    mainSvg.setAttribute('fill', 'none');
                }
                mainLikesCount.textContent = `${data.likes_count} likes`;
            }
        }
    } catch (error) {
        console.error('Error toggling like:', error);
    }
}

// Toggle share like in modal
async function toggleShareLikeInModal(shareId) {
    try {
        const response = await fetch(`/shares/${shareId}/like`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        const data = await response.json();
        
        if (data.success) {
            // Update modal like button
            const modalLikeBtn = document.getElementById(`modal-share-like-btn-${shareId}`);
            const modalLikesCount = document.getElementById(`modal-share-likes-count-${shareId}`);
            const svg = modalLikeBtn.querySelector('svg');
            
            if (data.liked) {
                svg.classList.add('text-red-500', 'fill-current');
                svg.setAttribute('fill', 'currentColor');
            } else {
                svg.classList.remove('text-red-500', 'fill-current');
                svg.setAttribute('fill', 'none');
            }
            
            if (modalLikesCount) {
                modalLikesCount.textContent = data.likes_count;
            }
        }
    } catch (error) {
        console.error('Error toggling share like:', error);
    }
}

// Toggle replies visibility
function toggleModalReplies(commentId) {
    const repliesList = document.getElementById(`modal-replies-list-${commentId}`);
    const arrow = document.getElementById(`modal-replies-arrow-${commentId}`);
    const text = document.getElementById(`modal-replies-text-${commentId}`);
    
    if (repliesList.classList.contains('hidden')) {
        repliesList.classList.remove('hidden');
        arrow.style.transform = 'rotate(180deg)';
        text.textContent = 'Hide replies';
    } else {
        repliesList.classList.add('hidden');
        arrow.style.transform = 'rotate(0deg)';
        const replyCount = repliesList.children.length;
        text.textContent = `View ${replyCount} ${replyCount === 1 ? 'reply' : 'replies'}`;
    }
}

// Toggle share replies visibility
function toggleModalShareReplies(commentId) {
    const repliesList = document.getElementById(`modal-share-replies-list-${commentId}`);
    const arrow = document.getElementById(`modal-share-replies-arrow-${commentId}`);
    const text = document.getElementById(`modal-share-replies-text-${commentId}`);
    
    if (repliesList.classList.contains('hidden')) {
        repliesList.classList.remove('hidden');
        arrow.style.transform = 'rotate(180deg)';
        text.textContent = 'Hide replies';
    } else {
        repliesList.classList.add('hidden');
        arrow.style.transform = 'rotate(0deg)';
        const replyCount = repliesList.children.length;
        text.textContent = `View ${replyCount} ${replyCount === 1 ? 'reply' : 'replies'}`;
    }
}

// Show reply form
function showModalReplyForm(commentId) {
    const replyForm = document.getElementById(`modal-reply-form-${commentId}`);
    replyForm.classList.remove('hidden');
    replyForm.querySelector('textarea').focus();
}

// Show share reply form
function showModalShareReplyForm(commentId) {
    const replyForm = document.getElementById(`modal-share-reply-form-${commentId}`);
    replyForm.classList.remove('hidden');
    replyForm.querySelector('textarea').focus();
}

// Hide reply form
function hideModalReplyForm(commentId) {
    const replyForm = document.getElementById(`modal-reply-form-${commentId}`);
    replyForm.classList.add('hidden');
    replyForm.querySelector('textarea').value = '';
}

// Hide share reply form
function hideModalShareReplyForm(commentId) {
    const replyForm = document.getElementById(`modal-share-reply-form-${commentId}`);
    replyForm.classList.add('hidden');
    replyForm.querySelector('textarea').value = '';
}

// Auto-resize textarea
function autoResizeTextarea(textarea) {
    textarea.style.height = 'auto';
    textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
}

// Initialize modal comment functionality
document.addEventListener('DOMContentLoaded', function() {
    // Handle modal comment form submissions
    document.addEventListener('submit', function(e) {
        if (e.target.classList.contains('modal-comment-form')) {
            e.preventDefault();
            submitModalComment(e.target);
        }
        
        if (e.target.classList.contains('modal-share-comment-form')) {
            e.preventDefault();
            submitModalShareComment(e.target);
        }
        
        if (e.target.classList.contains('modal-reply-comment-form')) {
            e.preventDefault();
            submitModalReply(e.target);
        }
        
        if (e.target.classList.contains('modal-share-reply-comment-form')) {
            e.preventDefault();
            submitModalShareReply(e.target);
        }
    });

    // Auto-resize textareas in modals
    document.addEventListener('input', function(e) {
        if (e.target.tagName === 'TEXTAREA' && e.target.closest('.modal-comment-form, .modal-share-comment-form, .modal-reply-comment-form, .modal-share-reply-comment-form')) {
            autoResizeTextarea(e.target);
        }
    });

    // Handle Enter key for comment submission
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && !e.shiftKey && e.target.tagName === 'TEXTAREA') {
            const form = e.target.closest('.modal-comment-form, .modal-share-comment-form, .modal-reply-comment-form, .modal-share-reply-comment-form');
            if (form && e.target.value.trim()) {
                e.preventDefault();
                form.dispatchEvent(new Event('submit', { bubbles: true }));
            }
        }
    });
});

// Submit modal comment
async function submitModalComment(form) {
    const postId = form.dataset.postId;
    const formData = new FormData(form);
    
    try {
        const response = await fetch(`/posts/${postId}/comments`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });
        
        const data = await response.json();
        
        if (data.success) {
            form.reset();
            addModalCommentToDOM(postId, data.comment);
            updateModalCommentCount(postId);
        } else {
            alert('Error posting comment: ' + (data.message || 'Unknown error'));
        }
    } catch (error) {
        console.error('Error submitting comment:', error);
        alert('Error posting comment. Please try again.');
    }
}

// Submit modal share comment
async function submitModalShareComment(form) {
    const shareId = form.dataset.shareId;
    const formData = new FormData(form);
    
    try {
        const response = await fetch(`/shares/${shareId}/comments`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });
        
        const data = await response.json();
        
        if (data.success) {
            form.reset();
            addModalShareCommentToDOM(shareId, data.comment);
            updateModalShareCommentCount(shareId);
        } else {
            alert('Error posting comment: ' + (data.message || 'Unknown error'));
        }
    } catch (error) {
        console.error('Error submitting share comment:', error);
        alert('Error posting comment. Please try again.');
    }
}

// Add comment to modal DOM
function addModalCommentToDOM(postId, comment) {
    const commentsList = document.getElementById(`modal-comments-list-${postId}`);
    const noCommentsDiv = commentsList.querySelector('.text-gray-400.text-center');

    if (noCommentsDiv) {
        noCommentsDiv.remove();
    }

    const commentHTML = createModalCommentHTML(comment);
    commentsList.insertAdjacentHTML('afterbegin', commentHTML);
}

// Add share comment to modal DOM
function addModalShareCommentToDOM(shareId, comment) {
    const commentsList = document.getElementById(`modal-share-comments-list-${shareId}`);
    const noCommentsDiv = commentsList.querySelector('.text-gray-400.text-center');

    if (noCommentsDiv) {
        noCommentsDiv.remove();
    }

    const commentHTML = createModalShareCommentHTML(comment);
    commentsList.insertAdjacentHTML('afterbegin', commentHTML);
}

// Update modal comment count
function updateModalCommentCount(postId) {
    const modalCountElement = document.getElementById(`modal-comments-count-${postId}`);
    const mainCountElement = document.getElementById(`comments-count-${postId}`);

    if (modalCountElement && mainCountElement) {
        const currentCount = parseInt(modalCountElement.textContent.split(' ')[0]) + 1;
        modalCountElement.textContent = `${currentCount} comments`;
        mainCountElement.textContent = `${currentCount} comments`;
    }
}

// Update modal share comment count
function updateModalShareCommentCount(shareId) {
    const modalCountElement = document.getElementById(`modal-share-comments-count-${shareId}`);
    const mainCountElement = document.getElementById(`share-comments-count-${shareId}`);

    if (modalCountElement && mainCountElement) {
        const currentCount = parseInt(modalCountElement.textContent.split(' ')[0]) + 1;
        modalCountElement.textContent = `${currentCount} comments`;
        mainCountElement.textContent = `${currentCount} comments`;
    }
}

// Create modal comment HTML
function createModalCommentHTML(comment) {
    const userAvatar = comment.user.avatar
        ? `/storage/${comment.user.avatar}`
        : `https://ui-avatars.com/api/?name=${encodeURIComponent(comment.user.name)}&color=7BC74D&background=EEEEEE`;

    const timeAgo = formatTimeAgo(comment.created_at);

    return `
        <div class="modal-comment-item py-4 px-4 hover:bg-gray-700/30 transition-colors duration-150" data-comment-id="${comment.id}">
            <div class="flex space-x-3">
                <a href="/profile/${comment.user.id}" class="flex-shrink-0">
                    <img class="h-8 w-8 rounded-full" src="${userAvatar}" alt="${comment.user.name}">
                </a>
                <div class="flex-1 min-w-0">
                    <div class="bg-gray-700 rounded-2xl px-4 py-3">
                        <div class="flex items-center space-x-2 mb-1">
                            <a href="/profile/${comment.user.id}" class="font-semibold text-white hover:text-custom-green text-sm hover:underline">
                                ${comment.user.name}
                            </a>
                        </div>
                        <div class="modal-comment-content">
                            <p class="text-gray-200 text-sm leading-relaxed">${comment.content.replace(/\n/g, '<br>')}</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4 mt-2 text-xs text-gray-400">
                        <span>${timeAgo}</span>
                        <button onclick="toggleModalCommentLike(${comment.id})" class="hover:text-red-400 transition-colors font-medium" id="modal-comment-like-btn-${comment.id}">
                            Like
                        </button>
                        <button onclick="showModalReplyForm(${comment.id})" class="hover:text-blue-400 transition-colors font-medium">
                            Reply
                        </button>
                    </div>
                    <div class="modal-reply-form hidden mt-3" id="modal-reply-form-${comment.id}">
                        <form class="modal-reply-comment-form" data-post-id="${comment.post_id}" data-parent-id="${comment.id}">
                            <input type="hidden" name="_token" value="${document.querySelector('meta[name="csrf-token"]').getAttribute('content')}">
                            <div class="flex space-x-3">
                                <img class="h-6 w-6 rounded-full" src="${userAvatar}" alt="Your avatar">
                                <div class="flex-1">
                                    <textarea name="content" rows="1" placeholder="Reply to ${comment.user.name}" class="w-full bg-gray-600 text-white border border-gray-500 rounded-full px-3 py-2 focus:outline-none focus:ring-2 focus:ring-custom-green focus:border-transparent resize-none text-sm" style="min-height: 32px;"></textarea>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Create modal share comment HTML
function createModalShareCommentHTML(comment) {
    const userAvatar = comment.user.avatar
        ? `/storage/${comment.user.avatar}`
        : `https://ui-avatars.com/api/?name=${encodeURIComponent(comment.user.name)}&color=7BC74D&background=EEEEEE`;

    const timeAgo = formatTimeAgo(comment.created_at);

    return `
        <div class="modal-share-comment-item py-4 px-4 hover:bg-gray-700/30 transition-colors duration-150" data-comment-id="${comment.id}">
            <div class="flex space-x-3">
                <a href="/profile/${comment.user.id}" class="flex-shrink-0">
                    <img class="h-8 w-8 rounded-full" src="${userAvatar}" alt="${comment.user.name}">
                </a>
                <div class="flex-1 min-w-0">
                    <div class="bg-gray-700 rounded-2xl px-4 py-3">
                        <div class="flex items-center space-x-2 mb-1">
                            <a href="/profile/${comment.user.id}" class="font-semibold text-white hover:text-custom-green text-sm hover:underline">
                                ${comment.user.name}
                            </a>
                        </div>
                        <div class="modal-share-comment-content">
                            <p class="text-gray-200 text-sm leading-relaxed">${comment.content.replace(/\n/g, '<br>')}</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4 mt-2 text-xs text-gray-400">
                        <span>${timeAgo}</span>
                        <button onclick="toggleModalShareCommentLike(${comment.id})" class="hover:text-red-400 transition-colors font-medium" id="modal-share-comment-like-btn-${comment.id}">
                            Like
                        </button>
                        <button onclick="showModalShareReplyForm(${comment.id})" class="hover:text-blue-400 transition-colors font-medium">
                            Reply
                        </button>
                    </div>
                    <div class="modal-share-reply-form hidden mt-3" id="modal-share-reply-form-${comment.id}">
                        <form class="modal-share-reply-comment-form" data-share-id="${comment.share_id}" data-parent-id="${comment.id}">
                            <input type="hidden" name="_token" value="${document.querySelector('meta[name="csrf-token"]').getAttribute('content')}">
                            <div class="flex space-x-3">
                                <img class="h-6 w-6 rounded-full" src="${userAvatar}" alt="Your avatar">
                                <div class="flex-1">
                                    <textarea name="content" rows="1" placeholder="Reply to ${comment.user.name}" class="w-full bg-gray-600 text-white border border-gray-500 rounded-full px-3 py-2 focus:outline-none focus:ring-2 focus:ring-custom-green focus:border-transparent resize-none text-sm" style="min-height: 32px;"></textarea>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Format time ago helper function
function formatTimeAgo(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 60) return 'just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d`;
    return `${Math.floor(diffInSeconds / 604800)}w`;
}

// Submit modal reply
async function submitModalReply(form) {
    const postId = form.dataset.postId;
    const parentId = form.dataset.parentId;
    const formData = new FormData(form);

    try {
        const response = await fetch(`/posts/${postId}/comments`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        const data = await response.json();

        if (data.success) {
            form.reset();
            hideModalReplyForm(parentId);
            addModalReplyToDOM(parentId, data.comment);
            updateModalCommentCount(postId);
        } else {
            alert('Error posting reply: ' + (data.message || 'Unknown error'));
        }
    } catch (error) {
        console.error('Error submitting reply:', error);
        alert('Error posting reply. Please try again.');
    }
}

// Submit modal share reply
async function submitModalShareReply(form) {
    const shareId = form.dataset.shareId;
    const parentId = form.dataset.parentId;
    const formData = new FormData(form);

    try {
        const response = await fetch(`/shares/${shareId}/comments`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        const data = await response.json();

        if (data.success) {
            form.reset();
            hideModalShareReplyForm(parentId);
            addModalShareReplyToDOM(parentId, data.comment);
            updateModalShareCommentCount(shareId);
        } else {
            alert('Error posting reply: ' + (data.message || 'Unknown error'));
        }
    } catch (error) {
        console.error('Error submitting share reply:', error);
        alert('Error posting reply. Please try again.');
    }
}

// Add modal reply to DOM
function addModalReplyToDOM(parentId, reply) {
    const repliesList = document.getElementById(`modal-replies-list-${parentId}`);
    if (!repliesList) return;

    const replyHTML = createModalReplyHTML(reply);
    repliesList.insertAdjacentHTML('beforeend', replyHTML);

    // Show replies if hidden
    if (repliesList.classList.contains('hidden')) {
        toggleModalReplies(parentId);
    }

    // Update reply count
    const viewRepliesBtn = document.getElementById(`modal-view-replies-btn-${parentId}`);
    const repliesText = document.getElementById(`modal-replies-text-${parentId}`);
    if (viewRepliesBtn && repliesText) {
        const replyCount = repliesList.children.length;
        repliesText.textContent = `Hide replies`;
    }
}

// Add modal share reply to DOM
function addModalShareReplyToDOM(parentId, reply) {
    const repliesList = document.getElementById(`modal-share-replies-list-${parentId}`);
    if (!repliesList) return;

    const replyHTML = createModalShareReplyHTML(reply);
    repliesList.insertAdjacentHTML('beforeend', replyHTML);

    // Show replies if hidden
    if (repliesList.classList.contains('hidden')) {
        toggleModalShareReplies(parentId);
    }

    // Update reply count
    const viewRepliesBtn = document.getElementById(`modal-share-view-replies-btn-${parentId}`);
    const repliesText = document.getElementById(`modal-share-replies-text-${parentId}`);
    if (viewRepliesBtn && repliesText) {
        const replyCount = repliesList.children.length;
        repliesText.textContent = `Hide replies`;
    }
}

// Create modal reply HTML
function createModalReplyHTML(reply) {
    const userAvatar = reply.user.avatar
        ? `/storage/${reply.user.avatar}`
        : `https://ui-avatars.com/api/?name=${encodeURIComponent(reply.user.name)}&color=7BC74D&background=EEEEEE`;

    const timeAgo = formatTimeAgo(reply.created_at);

    return `
        <div class="modal-reply-item py-2" data-reply-id="${reply.id}">
            <div class="flex space-x-2">
                <a href="/profile/${reply.user.id}" class="flex-shrink-0">
                    <img class="h-6 w-6 rounded-full" src="${userAvatar}" alt="${reply.user.name}">
                </a>
                <div class="flex-1 min-w-0">
                    <div class="bg-gray-700 rounded-xl px-3 py-2">
                        <div class="flex items-center space-x-2 mb-1">
                            <a href="/profile/${reply.user.id}" class="font-semibold text-white hover:text-custom-green text-xs hover:underline">
                                ${reply.user.name}
                            </a>
                        </div>
                        <p class="text-gray-200 text-xs leading-relaxed">${reply.content.replace(/\n/g, '<br>')}</p>
                    </div>
                    <div class="flex items-center space-x-3 mt-1 text-xs text-gray-400">
                        <span>${timeAgo}</span>
                        <button onclick="toggleModalReplyLike(${reply.id})" class="hover:text-red-400 transition-colors font-medium" id="modal-reply-like-btn-${reply.id}">
                            Like
                        </button>
                        <button onclick="showModalReplyForm(${reply.parent_id})" class="hover:text-blue-400 transition-colors font-medium">
                            Reply
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Create modal share reply HTML
function createModalShareReplyHTML(reply) {
    const userAvatar = reply.user.avatar
        ? `/storage/${reply.user.avatar}`
        : `https://ui-avatars.com/api/?name=${encodeURIComponent(reply.user.name)}&color=7BC74D&background=EEEEEE`;

    const timeAgo = formatTimeAgo(reply.created_at);

    return `
        <div class="modal-share-reply-item py-2" data-reply-id="${reply.id}">
            <div class="flex space-x-2">
                <a href="/profile/${reply.user.id}" class="flex-shrink-0">
                    <img class="h-6 w-6 rounded-full" src="${userAvatar}" alt="${reply.user.name}">
                </a>
                <div class="flex-1 min-w-0">
                    <div class="bg-gray-700 rounded-xl px-3 py-2">
                        <div class="flex items-center space-x-2 mb-1">
                            <a href="/profile/${reply.user.id}" class="font-semibold text-white hover:text-custom-green text-xs hover:underline">
                                ${reply.user.name}
                            </a>
                        </div>
                        <p class="text-gray-200 text-xs leading-relaxed">${reply.content.replace(/\n/g, '<br>')}</p>
                    </div>
                    <div class="flex items-center space-x-3 mt-1 text-xs text-gray-400">
                        <span>${timeAgo}</span>
                        <button onclick="toggleModalShareReplyLike(${reply.id})" class="hover:text-red-400 transition-colors font-medium" id="modal-share-reply-like-btn-${reply.id}">
                            Like
                        </button>
                        <button onclick="showModalShareReplyForm(${reply.parent_id})" class="hover:text-blue-400 transition-colors font-medium">
                            Reply
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Toggle modal comment like
async function toggleModalCommentLike(commentId) {
    try {
        const response = await fetch(`/comments/${commentId}/like`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        const data = await response.json();

        if (data.success) {
            const likeBtn = document.getElementById(`modal-comment-like-btn-${commentId}`);
            const likesCount = document.getElementById(`modal-comment-likes-count-${commentId}`);

            if (data.liked) {
                likeBtn.classList.add('text-red-400');
            } else {
                likeBtn.classList.remove('text-red-400');
            }

            if (likesCount) {
                if (data.likes_count > 0) {
                    likesCount.textContent = `${data.likes_count} ${data.likes_count === 1 ? 'like' : 'likes'}`;
                    likesCount.style.display = 'inline';
                } else {
                    likesCount.style.display = 'none';
                }
            }
        }
    } catch (error) {
        console.error('Error toggling comment like:', error);
    }
}

// Toggle modal share comment like
async function toggleModalShareCommentLike(commentId) {
    try {
        const response = await fetch(`/share-comments/${commentId}/like`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        const data = await response.json();

        if (data.success) {
            const likeBtn = document.getElementById(`modal-share-comment-like-btn-${commentId}`);
            const likesCount = document.getElementById(`modal-share-comment-likes-count-${commentId}`);

            if (data.liked) {
                likeBtn.classList.add('text-red-400');
            } else {
                likeBtn.classList.remove('text-red-400');
            }

            if (likesCount) {
                if (data.likes_count > 0) {
                    likesCount.textContent = `${data.likes_count} ${data.likes_count === 1 ? 'like' : 'likes'}`;
                    likesCount.style.display = 'inline';
                } else {
                    likesCount.style.display = 'none';
                }
            }
        }
    } catch (error) {
        console.error('Error toggling share comment like:', error);
    }
}

// Toggle modal reply like
async function toggleModalReplyLike(replyId) {
    try {
        const response = await fetch(`/comments/${replyId}/like`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        const data = await response.json();

        if (data.success) {
            const likeBtn = document.getElementById(`modal-reply-like-btn-${replyId}`);
            const likesCount = document.getElementById(`modal-reply-likes-count-${replyId}`);

            if (data.liked) {
                likeBtn.classList.add('text-red-400');
            } else {
                likeBtn.classList.remove('text-red-400');
            }

            if (likesCount) {
                if (data.likes_count > 0) {
                    likesCount.textContent = `${data.likes_count} ${data.likes_count === 1 ? 'like' : 'likes'}`;
                    likesCount.style.display = 'inline';
                } else {
                    likesCount.style.display = 'none';
                }
            }
        }
    } catch (error) {
        console.error('Error toggling reply like:', error);
    }
}

// Toggle modal share reply like
async function toggleModalShareReplyLike(replyId) {
    try {
        const response = await fetch(`/share-comments/${replyId}/like`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        const data = await response.json();

        if (data.success) {
            const likeBtn = document.getElementById(`modal-share-reply-like-btn-${replyId}`);
            const likesCount = document.getElementById(`modal-share-reply-likes-count-${replyId}`);

            if (data.liked) {
                likeBtn.classList.add('text-red-400');
            } else {
                likeBtn.classList.remove('text-red-400');
            }

            if (likesCount) {
                if (data.likes_count > 0) {
                    likesCount.textContent = `${data.likes_count} ${data.likes_count === 1 ? 'like' : 'likes'}`;
                    likesCount.style.display = 'inline';
                } else {
                    likesCount.style.display = 'none';
                }
            }
        }
    } catch (error) {
        console.error('Error toggling share reply like:', error);
    }
}

// Edit modal comment
function editModalComment(commentId) {
    const commentContent = document.querySelector(`[data-comment-id="${commentId}"] .modal-comment-content`);
    const editForm = document.querySelector(`[data-comment-id="${commentId}"] .modal-comment-edit-form`);

    if (commentContent && editForm) {
        commentContent.style.display = 'none';
        editForm.classList.remove('hidden');
        editForm.querySelector('textarea').focus();
    }
}

// Cancel edit modal comment
function cancelEditModalComment(commentId) {
    const commentContent = document.querySelector(`[data-comment-id="${commentId}"] .modal-comment-content`);
    const editForm = document.querySelector(`[data-comment-id="${commentId}"] .modal-comment-edit-form`);

    if (commentContent && editForm) {
        commentContent.style.display = 'block';
        editForm.classList.add('hidden');
    }
}

// Delete modal comment
async function deleteModalComment(commentId) {
    if (!confirm('Are you sure you want to delete this comment?')) {
        return;
    }

    try {
        const response = await fetch(`/comments/${commentId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        const data = await response.json();

        if (data.success) {
            const commentElement = document.querySelector(`[data-comment-id="${commentId}"]`);
            if (commentElement) {
                commentElement.remove();
            }
        } else {
            alert('Error deleting comment: ' + (data.message || 'Unknown error'));
        }
    } catch (error) {
        console.error('Error deleting comment:', error);
        alert('Error deleting comment. Please try again.');
    }
}
